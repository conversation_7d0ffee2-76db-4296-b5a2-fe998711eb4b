<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة ساحة الشريعة والقانون</title>
    <style>
        body { font-family: Arial; text-align: center; padding: 20px; }
        canvas { border: 2px solid #ccc; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🏛️ أيقونة ساحة الشريعة والقانون</h1>
    <canvas id="iconCanvas" width="512" height="512"></canvas>
    <br>
    <button onclick="downloadIcon()">📥 تحميل الأيقونة</button>
    
    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        function drawIcon() {
            // مسح الكانفاس
            ctx.clearRect(0, 0, 512, 512);
            
            // رسم الخلفية الدائرية
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#1E3A8A');
            gradient.addColorStop(1, '#3B82F6');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم الحدود الذهبية
            ctx.strokeStyle = '#F59E0B';
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // رسم قاعدة الميزان
            ctx.fillStyle = '#F59E0B';
            ctx.fillRect(196, 316, 120, 20);
            
            // رسم عمود الميزان
            ctx.fillRect(248, 176, 16, 140);
            
            // رسم الكفة اليسرى
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.moveTo(176, 216);
            ctx.lineTo(216, 216);
            ctx.lineTo(211, 236);
            ctx.lineTo(181, 236);
            ctx.closePath();
            ctx.fill();
            ctx.strokeStyle = '#F59E0B';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // رسم الكفة اليمنى
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.moveTo(296, 216);
            ctx.lineTo(336, 216);
            ctx.lineTo(331, 236);
            ctx.lineTo(301, 236);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // رسم خطوط الربط
            ctx.strokeStyle = '#F59E0B';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(196, 216);
            ctx.lineTo(248, 196);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(316, 216);
            ctx.lineTo(264, 196);
            ctx.stroke();
            
            // رسم كتاب صغير
            ctx.fillStyle = 'white';
            ctx.fillRect(226, 356, 60, 40);
            ctx.strokeStyle = '#F59E0B';
            ctx.lineWidth = 2;
            ctx.strokeRect(226, 356, 60, 40);
            
            // خطوط الكتاب
            ctx.strokeStyle = '#1E3A8A';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(234, 366);
            ctx.lineTo(278, 366);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(234, 376);
            ctx.lineTo(278, 376);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(234, 386);
            ctx.lineTo(278, 386);
            ctx.stroke();
            
            // إضافة النص
            ctx.fillStyle = 'white';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⚖️', 256, 150);
            
            ctx.font = 'bold 24px Arial';
            ctx.fillText('قانون', 256, 420);
            
            ctx.font = 'bold 16px Arial';
            ctx.fillText('ساحة الشريعة', 256, 445);
        }
        
        function downloadIcon() {
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'app_icon.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // رسم الأيقونة عند تحميل الصفحة
        drawIcon();
    </script>
</body>
</html>
