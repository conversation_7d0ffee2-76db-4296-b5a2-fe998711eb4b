# Script لبناء APK Debug وحل مشكلة المسار
# Build Debug APK and Fix Path Issue

Write-Host "🚀 بدء بناء APK Debug..." -ForegroundColor Green

# تنظيف البناء السابق
Write-Host "🧹 تنظيف البناء السابق..." -ForegroundColor Yellow
flutter clean

# بناء APK Debug
Write-Host "🔨 بناء APK Debug..." -ForegroundColor Yellow
flutter build apk --debug

# التحقق من وجود الملف في المكان الصحيح
$debugApkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
$flutterApkPath = "android\app\build\outputs\flutter-apk\app-debug.apk"

if (Test-Path $debugApkPath) {
    Write-Host "✅ تم العثور على APK في: $debugApkPath" -ForegroundColor Green
    
    # نسخ الملف إلى المكان المتوقع
    Write-Host "📋 نسخ الملف إلى المكان المتوقع..." -ForegroundColor Yellow
    Copy-Item $debugApkPath $flutterApkPath -Force
    
    # عرض معلومات الملف
    $fileInfo = Get-Item $flutterApkPath
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 1)
    
    Write-Host "🎉 تم بناء APK بنجاح!" -ForegroundColor Green
    Write-Host "📍 المسار: $flutterApkPath" -ForegroundColor Cyan
    Write-Host "📏 الحجم: $fileSizeMB MB" -ForegroundColor Cyan
    Write-Host "📅 التاريخ: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
    
} else {
    Write-Host "❌ لم يتم العثور على APK في: $debugApkPath" -ForegroundColor Red
    Write-Host "🔍 تحقق من سجلات البناء للأخطاء" -ForegroundColor Yellow
}

Write-Host "✨ انتهى البناء!" -ForegroundColor Green
