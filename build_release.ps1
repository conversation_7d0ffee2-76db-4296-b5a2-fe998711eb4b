# Script for building APK Release and fixing path issue
# Build Release APK and Fix Path Issue

Write-Host "Building APK Release..." -ForegroundColor Green

# Clean previous build
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
flutter clean

# Build APK Release
Write-Host "Building APK Release..." -ForegroundColor Yellow
flutter build apk --release

# التحقق من وجود الملف في المكان الصحيح
$releaseApkPath = "android\app\build\outputs\apk\release\app-release.apk"
$flutterApkPath = "android\app\build\outputs\flutter-apk\app-release.apk"

if (Test-Path $releaseApkPath) {
    Write-Host "APK found at: $releaseApkPath" -ForegroundColor Green

    # Copy file to expected location
    Write-Host "Copying file to expected location..." -ForegroundColor Yellow
    Copy-Item $releaseApkPath $flutterApkPath -Force
    
    # Display file information
    $fileInfo = Get-Item $flutterApkPath
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 1)

    Write-Host "APK built successfully!" -ForegroundColor Green
    Write-Host "Path: $flutterApkPath" -ForegroundColor Cyan
    Write-Host "Size: $fileSizeMB MB" -ForegroundColor Cyan
    Write-Host "Date: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan

} else {
    Write-Host "APK not found at: $releaseApkPath" -ForegroundColor Red
    Write-Host "Check build logs for errors" -ForegroundColor Yellow
}

Write-Host "Build completed!" -ForegroundColor Green
