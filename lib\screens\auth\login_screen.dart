import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';

import 'email_verification_waiting_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  bool _isSignUp = false;
  bool _obscurePassword = true;
  bool _rememberMe = true; // افتراضياً مفعل
  String _selectedAcademicYear = 'الفرقة الأولى';

  // قائمة الفرق الدراسية المتاحة
  final List<String> _availableAcademicYears = [
    'الفرقة الأولى',
    'الفرقة الثانية',
    'الفرقة الثالثة',
    'الفرقة الرابعة',
  ];

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800), // أسرع
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ), // أبسط
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1), // أقل حركة
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ), // أبسط
    );

    // تأخير بسيط لتحسين الأداء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6366F1)],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLogo(),
                      const SizedBox(height: 40),
                      Consumer2<AuthProvider, ThemeProvider>(
                        builder: (context, authProvider, themeProvider, child) {
                          return _buildLoginCard(authProvider, themeProvider);
                        },
                      ),
                      const SizedBox(height: 24),
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          return _buildSocialButtons(authProvider);
                        },
                      ),
                      const SizedBox(height: 16),
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          return _buildSkipButton(authProvider);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 10, // أقل blur
                offset: const Offset(0, 5), // أقل offset
              ),
            ],
          ),
          child: const Icon(Icons.balance, size: 50, color: Colors.white),
        ),
        const SizedBox(height: 16),
        Text(
          'Legal 2025',
          style: GoogleFonts.cairo(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          'تطبيق الشريعة والقانون',
          style: GoogleFonts.cairo(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginCard(
    AuthProvider authProvider,
    ThemeProvider themeProvider,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              _isSignUp ? 'إنشاء حساب جديد' : 'تسجيل الدخول',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // حقل الاسم (للتسجيل فقط)
            if (_isSignUp) ...[
              _buildTextField(
                controller: _nameController,
                label: 'الاسم الكامل',
                icon: Icons.person,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال الاسم الكامل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // حقل الفرقة الدراسية
              _buildAcademicYearDropdown(),
              const SizedBox(height: 16),
            ],

            // حقل البريد الإلكتروني
            _buildTextField(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'يرجى إدخال البريد الإلكتروني';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value!)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // حقل كلمة المرور
            _buildTextField(
              controller: _passwordController,
              label: 'كلمة المرور',
              icon: Icons.lock,
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'يرجى إدخال كلمة المرور';
                }
                if (value!.length < 6) {
                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // خيار "تذكرني" (فقط في تسجيل الدخول)
            if (!_isSignUp) ...[
              Row(
                children: [
                  Checkbox(
                    value: _rememberMe,
                    onChanged: (value) {
                      setState(() {
                        _rememberMe = value ?? true;
                      });
                    },
                    activeColor: Colors.white,
                    checkColor: const Color(0xFF6366F1),
                    side: BorderSide(
                      color: Colors.white.withValues(alpha: 0.7),
                      width: 2,
                    ),
                  ),
                  Text(
                    'تذكرني لمدة 30 يوم',
                    style: GoogleFonts.cairo(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // رسائل الخطأ والنجاح
            if (authProvider.error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.5)),
                ),
                child: Text(
                  authProvider.error!,
                  style: GoogleFonts.cairo(
                    color: Colors.red[100],
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
            ],

            if (authProvider.successMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.5),
                  ),
                ),
                child: Text(
                  authProvider.successMessage!,
                  style: GoogleFonts.cairo(
                    color: Colors.green[100],
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
            ],

            // زر تسجيل الدخول/إنشاء الحساب
            _buildMainButton(authProvider),
            const SizedBox(height: 16),

            // زر نسيت كلمة المرور (يظهر فقط في وضع تسجيل الدخول)
            if (!_isSignUp) _buildForgotPasswordButton(authProvider),
            if (!_isSignUp) const SizedBox(height: 16),

            // زر التبديل بين تسجيل الدخول والتسجيل
            _buildToggleButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      style: GoogleFonts.cairo(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: GoogleFonts.cairo(
          color: Colors.white.withValues(alpha: 0.8),
        ),
        prefixIcon: Icon(icon, color: Colors.white.withValues(alpha: 0.7)),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.1),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white, width: 1),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.red.withValues(alpha: 0.7)),
        ),
        errorStyle: GoogleFonts.cairo(color: Colors.red[200]),
      ),
    );
  }

  Widget _buildAcademicYearDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedAcademicYear,
        decoration: InputDecoration(
          labelText: 'الفرقة الدراسية',
          labelStyle: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.school,
            color: Colors.white.withValues(alpha: 0.7),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
        dropdownColor: const Color(0xFF1E293B),
        items:
            _availableAcademicYears.map((String year) {
              return DropdownMenuItem<String>(
                value: year,
                child: Text(
                  year,
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              );
            }).toList(),
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedAcademicYear = newValue;
            });
          }
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى اختيار الفرقة الدراسية';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildMainButton(AuthProvider authProvider) {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: authProvider.isLoading ? null : _handleMainAction,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF6366F1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2, // أقل ظل
        ),
        child:
            authProvider.isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFF6366F1),
                    ),
                  ),
                )
                : Text(
                  _isSignUp ? 'إرسال رابط التحقق' : 'تسجيل الدخول',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
      ),
    );
  }

  Widget _buildForgotPasswordButton(AuthProvider authProvider) {
    return TextButton(
      onPressed:
          authProvider.isLoading
              ? null
              : () => _showForgotPasswordDialog(authProvider),
      child: Text(
        'نسيت كلمة المرور؟',
        style: GoogleFonts.cairo(
          color: Colors.white.withValues(alpha: 0.9),
          fontSize: 14,
          fontWeight: FontWeight.w500,
          decoration: TextDecoration.underline,
          decorationColor: Colors.white.withValues(alpha: 0.9),
        ),
      ),
    );
  }

  Widget _buildToggleButton() {
    return TextButton(
      onPressed: () {
        setState(() {
          _isSignUp = !_isSignUp;
          _verificationCodeController.clear();
        });
      },
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: GoogleFonts.cairo(color: Colors.white.withValues(alpha: 0.8)),
          children: [
            TextSpan(
              text: _isSignUp ? 'لديك حساب بالفعل؟ ' : 'ليس لديك حساب؟ ',
            ),
            TextSpan(
              text: _isSignUp ? 'تسجيل الدخول' : 'إنشاء حساب',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialButtons(AuthProvider authProvider) {
    return Column(
      children: [
        Text(
          'أو',
          style: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 50,
          child: ElevatedButton.icon(
            onPressed:
                authProvider.isLoading
                    ? null
                    : () => _signInWithGoogle(authProvider),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 1, // أقل ظل
            ),
            icon: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text(
                  'G',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            label: Text(
              'تسجيل الدخول بـ Google',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkipButton(AuthProvider authProvider) {
    return SizedBox(
      height: 50,
      child: OutlinedButton(
        onPressed:
            authProvider.isLoading ? null : () => _signInAsGuest(authProvider),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.white, width: 1), // أقل سمك
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'تسجيل الدخول كضيف',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Future<void> _handleMainAction() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (_isSignUp) {
      // إنشاء الحساب مباشرة مع إرسال إيميل التحقق
      final success = await authProvider.createAccount(
        email: _emailController.text,
        password: _passwordController.text,
        displayName: _nameController.text,
        verificationCode: '000000', // كود وهمي لأن النظام الجديد لا يحتاجه
        academicYear: _selectedAcademicYear,
      );

      if (success && mounted) {
        // الانتقال إلى صفحة انتظار التحقق
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => EmailVerificationWaitingScreen(
                  email: _emailController.text,
                  password: _passwordController.text,
                ),
          ),
        );
      } else {
        // عرض رسالة خطأ مفصلة
        _showErrorDialog(authProvider.error ?? 'فشل في إنشاء الحساب');
      }
    } else {
      // تسجيل الدخول
      final success = await authProvider.signInWithEmail(
        _emailController.text,
        _passwordController.text,
        rememberMe: _rememberMe,
      );

      if (success) {
        _navigateToHome();
      } else {
        // إذا فشل تسجيل الدخول بسبب عدم التفعيل، اعرض خيار تفعيل الحسابات القديمة
        final error = authProvider.error ?? '';
        if (error.contains('تفعيل حسابك')) {
          _showOldAccountActivationDialog();
        }
      }
    }
  }

  Future<void> _signInWithGoogle(AuthProvider authProvider) async {
    final success = await authProvider.signInWithGoogle();
    if (success) {
      _navigateToHome();
    }
  }

  Future<void> _signInAsGuest(AuthProvider authProvider) async {
    final success = await authProvider.signInAnonymously();
    if (success) {
      _navigateToHome();
    }
  }

  void _navigateToHome() {
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/home');
    }
  }

  void _showErrorDialog(String errorMessage) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 28),
                const SizedBox(width: 12),
                Text(
                  'خطأ في التسجيل',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(errorMessage, style: GoogleFonts.cairo(fontSize: 16)),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نصائح لحل المشكلة:',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildInstructionItem('تأكد من صحة البريد الإلكتروني'),
                      _buildInstructionItem('تحقق من اتصال الإنترنت'),
                      _buildInstructionItem('حاول مرة أخرى بعد دقائق قليلة'),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'حسناً',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Text(text, style: GoogleFonts.cairo(fontSize: 14)),
        ],
      ),
    );
  }

  void _showForgotPasswordDialog(AuthProvider authProvider) {
    if (!mounted) return;

    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Text(
              'إعادة ضبط كلمة المرور',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF1E293B),
              ),
              textAlign: TextAlign.center,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة ضبط كلمة المرور',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: const Color(0xFF64748B),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                TextField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  textAlign: TextAlign.right,
                  decoration: InputDecoration(
                    hintText: 'البريد الإلكتروني',
                    hintStyle: GoogleFonts.cairo(
                      color: const Color(0xFF94A3B8),
                    ),
                    prefixIcon: const Icon(
                      Icons.email_outlined,
                      color: Color(0xFF6366F1),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF6366F1),
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(
                    color: const Color(0xFF64748B),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  final email = emailController.text.trim();
                  if (email.isNotEmpty) {
                    Navigator.of(context).pop();
                    final success = await authProvider.sendPasswordResetEmail(
                      email,
                    );
                    if (success) {
                      _showSnackBar(
                        'تم إرسال رابط إعادة ضبط كلمة المرور إلى بريدك الإلكتروني',
                      );
                    } else {
                      _showSnackBar(
                        authProvider.error ?? 'فشل في إرسال الرابط',
                        isError: true,
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'إرسال',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: GoogleFonts.cairo(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          backgroundColor: isError ? Colors.red : Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  /// إظهار حوار تفعيل الحسابات القديمة
  void _showOldAccountActivationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'تفعيل حساب قديم',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'يبدو أن لديك حساب مسجل مسبقاً قبل تطبيق نظام التفعيل الجديد.\n\nهل تريد تفعيل حسابك القديم تلقائياً؟',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _activateOldAccount();
                },
                child: Text(
                  'تفعيل الحساب',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  /// تفعيل الحساب القديم
  Future<void> _activateOldAccount() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.activateOldAccountManually(
      _emailController.text,
      _passwordController.text,
    );

    if (success) {
      _showSnackBar('تم تفعيل حسابك بنجاح!');
      // محاولة تسجيل الدخول مرة أخرى
      Future.delayed(const Duration(seconds: 1), () {
        _navigateToHome();
      });
    } else {
      _showSnackBar(authProvider.error ?? 'فشل في تفعيل الحساب', isError: true);
    }
  }
}
