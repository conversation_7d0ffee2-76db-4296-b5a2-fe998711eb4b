#!/usr/bin/env python3
"""
إنشاء أيقونة قانونية لتطبيق ساحة الشريعة والقانون
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_legal_icon():
    """إنشاء أيقونة قانونية مع رمز الميزان والكتاب"""
    
    # إعدادات الأيقونة
    size = 512
    bg_color = "#1E3A8A"  # أزرق داكن قانوني
    accent_color = "#F59E0B"  # ذهبي
    text_color = "#FFFFFF"  # أبيض
    
    # إنشاء الصورة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم الخلفية الدائرية
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=bg_color, outline=accent_color, width=8)
    
    # رسم رمز الميزان (مبسط)
    center_x, center_y = size // 2, size // 2
    
    # قاعدة الميزان
    base_y = center_y + 80
    draw.rectangle([center_x-60, base_y-10, center_x+60, base_y+10], 
                  fill=accent_color)
    
    # عمود الميزان
    draw.rectangle([center_x-8, center_y-60, center_x+8, base_y], 
                  fill=accent_color)
    
    # كفتي الميزان
    scale_y = center_y - 20
    
    # الكفة اليسرى
    left_points = [
        (center_x-80, scale_y),
        (center_x-40, scale_y),
        (center_x-45, scale_y+20),
        (center_x-75, scale_y+20)
    ]
    draw.polygon(left_points, fill=text_color, outline=accent_color, width=2)
    
    # الكفة اليمنى
    right_points = [
        (center_x+40, scale_y),
        (center_x+80, scale_y),
        (center_x+75, scale_y+20),
        (center_x+45, scale_y+20)
    ]
    draw.polygon(right_points, fill=text_color, outline=accent_color, width=2)
    
    # خطوط الربط
    draw.line([center_x-60, scale_y, center_x-8, center_y-40], 
             fill=accent_color, width=3)
    draw.line([center_x+60, scale_y, center_x+8, center_y-40], 
             fill=accent_color, width=3)
    
    # رسم كتاب صغير في الأسفل
    book_x = center_x - 30
    book_y = center_y + 100
    book_w = 60
    book_h = 40
    
    # غلاف الكتاب
    draw.rectangle([book_x, book_y, book_x+book_w, book_y+book_h], 
                  fill=text_color, outline=accent_color, width=2)
    
    # خطوط الصفحات
    for i in range(3):
        y = book_y + 10 + i * 8
        draw.line([book_x+8, y, book_x+book_w-8, y], 
                 fill=bg_color, width=2)
    
    # إضافة نص عربي مبسط (إذا توفر خط عربي)
    try:
        # محاولة استخدام خط عربي
        font_size = 24
        font = ImageFont.truetype("arial.ttf", font_size)
        
        # كتابة "قانون" في الأعلى
        text = "قانون"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (size - text_width) // 2
        text_y = center_y - 120
        
        draw.text((text_x, text_y), text, fill=text_color, font=font)
        
    except:
        # إذا لم يتوفر خط عربي، استخدم رموز بديلة
        font_size = 36
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        # رمز القانون
        text = "⚖"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (size - text_width) // 2
        text_y = center_y - 120
        
        draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    return img

def create_android_icons():
    """إنشاء أيقونات Android بأحجام مختلفة"""
    
    # إنشاء الأيقونة الأساسية
    base_icon = create_legal_icon()
    
    # أحجام Android المطلوبة
    android_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    # إنشاء المجلدات وحفظ الأيقونات
    android_res_path = "android/app/src/main/res"
    
    for folder, size in android_sizes.items():
        folder_path = os.path.join(android_res_path, folder)
        os.makedirs(folder_path, exist_ok=True)
        
        # تغيير حجم الأيقونة
        resized_icon = base_icon.resize((size, size), Image.Resampling.LANCZOS)
        
        # حفظ الأيقونة
        icon_path = os.path.join(folder_path, "ic_launcher.png")
        resized_icon.save(icon_path, "PNG")
        print(f"تم إنشاء: {icon_path}")
    
    # حفظ أيقونة كبيرة للاستخدام العام
    base_icon.save("assets/icon/app_icon.png", "PNG")
    print("تم إنشاء: assets/icon/app_icon.png")
    
    # حفظ أيقونة للـ Play Store (512x512)
    base_icon.save("app_icon_playstore.png", "PNG")
    print("تم إنشاء: app_icon_playstore.png")

if __name__ == "__main__":
    print("🎨 إنشاء أيقونة ساحة الشريعة والقانون...")
    
    try:
        create_android_icons()
        print("✅ تم إنشاء جميع الأيقونات بنجاح!")
        print("\nالأيقونات المنشأة:")
        print("- أيقونات Android في مجلدات mipmap")
        print("- assets/icon/app_icon.png")
        print("- app_icon_playstore.png (للـ Play Store)")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونات: {e}")
        print("تأكد من تثبيت مكتبة Pillow: pip install Pillow")
