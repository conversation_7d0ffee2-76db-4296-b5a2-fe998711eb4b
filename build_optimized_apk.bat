@echo off
echo ========================================
echo    بناء APK محسن - جميع الاصدارات
echo    تم إصلاح جميع الأخطاء ✅
echo ========================================
echo.

REM تنظيف المشروع
echo [1/6] تنظيف المشروع...
call flutter clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    pause
    exit /b 1
)

REM تحديث التبعيات
echo [2/6] تحديث التبعيات...
call flutter pub get
if %errorlevel% neq 0 (
    echo خطأ في تحديث التبعيات
    pause
    exit /b 1
)

REM بناء APK للتطوير
echo [3/6] بناء APK للتطوير...
call flutter build apk --debug --target-platform android-arm64
if %errorlevel% neq 0 (
    echo خطأ في بناء APK للتطوير
    pause
    exit /b 1
)

REM بناء APK للإنتاج - Universal
echo [4/6] بناء APK للإنتاج (Universal)...
call flutter build apk --release
if %errorlevel% neq 0 (
    echo خطأ في بناء APK للإنتاج
    pause
    exit /b 1
)

REM بناء APK للإنتاج - Split per ABI
echo [5/6] بناء APK للإنتاج (Split per ABI)...
call flutter build apk --release --split-per-abi
if %errorlevel% neq 0 (
    echo خطأ في بناء APK Split
    pause
    exit /b 1
)

REM بناء App Bundle
echo [6/6] بناء App Bundle...
call flutter build appbundle --release
if %errorlevel% neq 0 (
    echo خطأ في بناء App Bundle
    pause
    exit /b 1
)

echo.
echo ========================================
echo           البناء مكتمل بنجاح!
echo ========================================
echo.

REM عرض معلومات الملفات المبنية
echo ملفات APK المبنية:
echo.
echo 1. للتطوير:
dir /b "build\app\outputs\flutter-apk\app-debug.apk" 2>nul && (
    for %%i in ("build\app\outputs\flutter-apk\app-debug.apk") do echo    - app-debug.apk [%%~zi bytes]
) || echo    - غير موجود

echo.
echo 2. للإنتاج (Universal):
dir /b "build\app\outputs\flutter-apk\app-release.apk" 2>nul && (
    for %%i in ("build\app\outputs\flutter-apk\app-release.apk") do echo    - app-release.apk [%%~zi bytes]
) || echo    - غير موجود

echo.
echo 3. للإنتاج (Split per ABI):
dir /b "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" 2>nul && (
    for %%i in ("build\app\outputs\flutter-apk\app-arm64-v8a-release.apk") do echo    - app-arm64-v8a-release.apk [%%~zi bytes]
) || echo    - غير موجود

dir /b "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" 2>nul && (
    for %%i in ("build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk") do echo    - app-armeabi-v7a-release.apk [%%~zi bytes]
) || echo    - غير موجود

dir /b "build\app\outputs\flutter-apk\app-x86_64-release.apk" 2>nul && (
    for %%i in ("build\app\outputs\flutter-apk\app-x86_64-release.apk") do echo    - app-x86_64-release.apk [%%~zi bytes]
) || echo    - غير موجود

echo.
echo 4. App Bundle:
dir /b "build\app\outputs\bundle\release\app-release.aab" 2>nul && (
    for %%i in ("build\app\outputs\bundle\release\app-release.aab") do echo    - app-release.aab [%%~zi bytes]
) || echo    - غير موجود

echo.
echo ========================================
echo مسارات الملفات:
echo ========================================
echo APK Files: build\app\outputs\flutter-apk\
echo AAB Files: build\app\outputs\bundle\release\
echo.

REM فتح مجلد الإخراج
echo هل تريد فتح مجلد الإخراج؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    start explorer "build\app\outputs\flutter-apk\"
)

echo.
echo تم الانتهاء من البناء بنجاح!
pause
