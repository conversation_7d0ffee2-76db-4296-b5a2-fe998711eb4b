<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة ساحة الشريعة والقانون</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        
        .icon-preview {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            border-radius: 50px;
            background: linear-gradient(135deg, #1E3A8A, #3B82F6);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
        }
        
        .scale-icon {
            font-size: 60px;
            color: #F59E0B;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .book-icon {
            font-size: 30px;
            color: white;
            position: absolute;
            bottom: 30px;
        }
        
        .app-name {
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        h1 {
            color: #1E3A8A;
            margin-bottom: 10px;
        }
        
        .description {
            color: #6B7280;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #1E3A8A, #3B82F6);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            background: #F3F4F6;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #1E3A8A;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ ساحة الشريعة والقانون</h1>
        <p class="description">
            أيقونة التطبيق الجديدة - تصميم قانوني أنيق يجمع بين رمز الميزان والكتاب
        </p>
        
        <div class="icon-preview" id="iconPreview">
            <div class="scale-icon">⚖️</div>
            <div class="book-icon">📚</div>
            <div class="app-name">ساحة الشريعة والقانون</div>
        </div>
        
        <button class="download-btn" onclick="downloadIcon()">
            📥 تحميل الأيقونة PNG
        </button>
        
        <button class="download-btn" onclick="generateAndroidIcons()">
            📱 إنشاء أيقونات Android
        </button>
        
        <div class="instructions">
            <h3>📋 خطوات التطبيق:</h3>
            <div class="step">
                <strong>1.</strong> اضغط على "تحميل الأيقونة PNG" لحفظ الأيقونة
            </div>
            <div class="step">
                <strong>2.</strong> انسخ الأيقونة إلى مجلد <code>assets/icon/app_icon.png</code>
            </div>
            <div class="step">
                <strong>3.</strong> استخدم أداة flutter_launcher_icons لإنشاء أيقونات Android
            </div>
            <div class="step">
                <strong>4.</strong> قم ببناء التطبيق: <code>flutter build apk --release</code>
            </div>
        </div>
    </div>

    <script>
        function downloadIcon() {
            // إنشاء canvas لرسم الأيقونة
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 512;
            
            // رسم الخلفية
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#1E3A8A');
            gradient.addColorStop(1, '#3B82F6');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم حدود ذهبية
            ctx.strokeStyle = '#F59E0B';
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // إضافة النص
            ctx.fillStyle = '#F59E0B';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⚖️', 256, 200);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 60px Arial';
            ctx.fillText('📚', 256, 320);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('ساحة الشريعة والقانون', 256, 400);
            
            // تحميل الصورة
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'app_icon_legal.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        function generateAndroidIcons() {
            alert('لإنشاء أيقونات Android:\n\n1. احفظ الأيقونة أولاً\n2. استخدم flutter_launcher_icons\n3. أو انسخ الأيقونة يدوياً إلى مجلدات mipmap');
        }
    </script>
</body>
</html>
